import axios, { type AxiosInstance, type AxiosError } from 'axios'
import type {
  UserResponse,
  Customer,
  CreateCustomerRequest,
  UpdateCustomerRequest,
  ApiResponse,
  InnerApiResponse
} from '@/types'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://cmsapi.jenexusenergy.com/api/v1'

// API Client class
class ApiClient {
  private axiosInstance: AxiosInstance

  constructor(baseURL: string) {
    this.axiosInstance = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
    })

    // Add response interceptor for consistent error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        console.error('API Error:', error)
        return Promise.reject(error)
      }
    )
  }

  // Set authorization token
  setAuthToken(token: string) {
    this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${token}`
  }

  // Remove authorization token
  removeAuthToken() {
    delete this.axiosInstance.defaults.headers.common['Authorization']
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.request({
        url: endpoint,
        method,
        data,
      })

      const rawData = response.data

      // Handle the server response structure
      // The server returns: { success: true, data: actualData } or { success: false, error: errorMessage }
      if (rawData.success && rawData.data !== undefined) {
        // Wrap the response to match our expected structure
        return {
          success: true,
          data: {
            success: true,
            data: rawData.data,
            timestamp: new Date().toISOString()
          } as InnerApiResponse<T>
        }
      } else {
        return {
          success: false,
          error: rawData.error || rawData.message || 'API request failed',
        }
      }
    } catch (error) {
      // Handle Axios errors
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError
        if (axiosError.response) {
          // Server responded with error status
          const errorData = axiosError.response.data as any
          return {
            success: false,
            error: errorData?.message || errorData?.error || `HTTP error! status: ${axiosError.response.status}`,
          }
        } else if (axiosError.request) {
          // Request was made but no response received
          return {
            success: false,
            error: 'Network error: No response from server',
          }
        } else {
          // Something else happened
          return {
            success: false,
            error: axiosError.message || 'Request setup error',
          }
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }
    }
  }

  // GET request
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'GET')
  }

  // POST request
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'POST', data)
  }

  // PUT request
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'PUT', data)
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'DELETE')
  }


}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL)

// User API Service
export class UserService {
  // Get user by ID
  static async getUserById(id: string): Promise<ApiResponse<UserResponse>> {
    return apiClient.get<UserResponse>(`/users/${id}`)
  }

  // Get all users
  static async getUsers(): Promise<ApiResponse<UserResponse[]>> {
    return apiClient.get<UserResponse[]>('/users')
  }

  // Update user profile
  static async updateUser(id: string, data: Partial<UserResponse>): Promise<ApiResponse<UserResponse>> {
    return apiClient.put<UserResponse>(`/users/${id}`, data)
  }
}

// Customer API Service
export class CustomerService {
  // Get all customers
  static async getCustomers(): Promise<ApiResponse<Customer[]>> {
    return apiClient.get<Customer[]>('/customers')
  }

  // Get customer by ID
  static async getCustomerById(id: string): Promise<ApiResponse<Customer>> {
    return apiClient.get<Customer>(`/customers/${id}`)
  }

  // Create new customer
  static async createCustomer(data: CreateCustomerRequest): Promise<ApiResponse<Customer>> {
    return apiClient.post<Customer>('/customers', data)
  }

  // Update customer
  static async updateCustomer(id: string, data: UpdateCustomerRequest): Promise<ApiResponse<Customer>> {
    return apiClient.put<Customer>(`/customers/${id}`, data)
  }

  // Delete customer (soft delete)
  static async deleteCustomer(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/customers/${id}`)
  }

  // Search customers
  static async searchCustomers(query: string): Promise<ApiResponse<Customer[]>> {
    return apiClient.get<Customer[]>(`/customers/search?q=${encodeURIComponent(query)}`)
  }

  // Get customers by parent ID
  static async getCustomersByParent(parentId: string): Promise<ApiResponse<Customer[]>> {
    return apiClient.get<Customer[]>(`/customers?parentId=${parentId}`)
  }
}

// Auth Service
export class AuthService {
  // Login
  static async login(credentials: { login: string; password: string }): Promise<ApiResponse<{ token: string; user: UserResponse }>> {
    return apiClient.post<{ token: string; user: UserResponse }>('/auth/login', credentials)
  }

  // Logout
  static async logout(): Promise<ApiResponse<void>> {
    const result = await apiClient.post<void>('/auth/logout')
    apiClient.removeAuthToken()
    return result
  }

  // Get current user profile
  static async getCurrentUser(): Promise<ApiResponse<UserResponse>> {
    return apiClient.get<UserResponse>('/auth/profile')
  }

  // Set token for authenticated requests
  static setToken(token: string) {
    apiClient.setAuthToken(token)
  }

  // Remove token
  static removeToken() {
    apiClient.removeAuthToken()
  }
}

// Export the API client for direct use if needed
export { apiClient as default }
