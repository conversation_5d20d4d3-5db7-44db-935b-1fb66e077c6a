import { ref, type Ref } from 'vue'
import type { ApiResponse } from '@/types'

export interface UseApiReturn<T> {
  data: Ref<T | null>
  loading: Ref<boolean>
  error: Ref<string | null>
  execute: (...args: any[]) => Promise<T | null>
  reset: () => void
}

export function useApi<T>(
  apiFunction: (...args: any[]) => Promise<ApiResponse<T>>
): UseApiReturn<T> {
  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  const execute = async (...args: any[]): Promise<T | null> => {
    loading.value = true
    error.value = null
    data.value = null

    try {
      const response = await apiFunction(...args)
      
      // Handle nested response structure
      if (response.success && response.data && response.data.success && response.data.data !== null) {
        data.value = response.data.data
        return response.data.data
      } else if (response.success && response.data && response.data.success && response.data.data === null) {
        // Handle case where API returns null data (e.g., empty list)
        data.value = null
        return null
      } else {
        error.value = response.error || (response.data && !response.data.success ? 'API request failed' : 'An error occurred')
        return null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error occurred'
      return null
    } finally {
      loading.value = false
    }
  }

  const reset = () => {
    data.value = null
    loading.value = false
    error.value = null
  }

  return {
    data: data as Ref<T | null>,
    loading,
    error,
    execute,
    reset
  }
}

// Specialized hook for list operations
export function useApiList<T>(
  apiFunction: (...args: any[]) => Promise<ApiResponse<T[]>>
): UseApiReturn<T[]> {
  return useApi<T[]>(apiFunction)
}

// Hook for handling form submissions
export function useApiMutation<TInput, TOutput>(
  apiFunction: (input: TInput) => Promise<ApiResponse<TOutput>>
) {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const success = ref(false)

  const mutate = async (input: TInput): Promise<TOutput | null> => {
    loading.value = true
    error.value = null
    success.value = false

    try {
      const response = await apiFunction(input)
      
      // Handle nested response structure
      if (response.success && response.data && response.data.success && response.data.data !== null) {
        success.value = true
        return response.data.data
      } else if (response.success && response.data && response.data.success && response.data.data === null) {
        // Handle case where API returns null data (e.g., delete operations)
        success.value = true
        return null
      } else {
        error.value = response.error || (response.data && !response.data.success ? 'API request failed' : 'An error occurred')
        return null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error occurred'
      return null
    } finally {
      loading.value = false
    }
  }

  const reset = () => {
    loading.value = false
    error.value = null
    success.value = false
  }

  return {
    loading,
    error,
    success,
    mutate,
    reset
  }
}
