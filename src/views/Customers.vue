<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-2xl font-bold text-gray-900">Customers</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Customer
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 tablet:p-6 desktop:p-8 rounded-lg shadow">
      <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search customers..."
            class="w-full px-3 tablet:px-4 desktop:px-5 py-2 tablet:py-3 desktop:py-4 text-sm tablet:text-base desktop:text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <button
          @click="clearSearch"
          class="px-4 tablet:px-6 desktop:px-8 py-2 tablet:py-3 desktop:py-4 text-sm tablet:text-base desktop:text-lg font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
        >
          Clear
        </button>
      </div>
    </div>

    <!-- Customers Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 tablet:px-8 desktop:px-10 py-4 tablet:py-6 desktop:py-8 border-b border-gray-200">
        <h3 class="text-lg tablet:text-xl desktop:text-2xl font-medium text-gray-900">
          All Customers ({{ customers.length }})
        </h3>
      </div>

      <div v-if="customers.length === 0" class="p-6 text-center text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
        </svg>
        <p class="mt-2">No customers found</p>
        <p class="text-sm text-gray-400">Get started by adding your first customer</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 tablet:px-8 desktop:px-10 py-3 tablet:py-4 desktop:py-5 text-left text-xs tablet:text-sm desktop:text-base font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th class="hidden tablet:table-cell px-6 tablet:px-8 desktop:px-10 py-3 tablet:py-4 desktop:py-5 text-left text-xs tablet:text-sm desktop:text-base font-medium text-gray-500 uppercase tracking-wider">
                Company
              </th>
              <th class="px-6 tablet:px-8 desktop:px-10 py-3 tablet:py-4 desktop:py-5 text-left text-xs tablet:text-sm desktop:text-base font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th class="hidden desktop:table-cell px-6 tablet:px-8 desktop:px-10 py-3 tablet:py-4 desktop:py-5 text-left text-xs tablet:text-sm desktop:text-base font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th class="px-6 tablet:px-8 desktop:px-10 py-3 tablet:py-4 desktop:py-5 text-right text-xs tablet:text-sm desktop:text-base font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="(customer, index) in customers"
              :key="index"
              class="hover:bg-gray-50 transition-colors"
            >
              <td class="px-6 tablet:px-6 desktop:px-8 py-4 tablet:py-4 desktop:py-5 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 tablet:h-11 tablet:w-11 desktop:h-12 desktop:w-12 rounded-full bg-blue-500 flex items-center justify-center">
                    <span class="text-sm tablet:text-base desktop:text-base font-medium text-white">
                      {{ customer.name?.charAt(0)?.toUpperCase() || '?' }}
                    </span>
                  </div>
                  <div class="ml-4 tablet:ml-4 desktop:ml-5">
                    <div class="text-sm tablet:text-base desktop:text-base font-medium text-gray-900">{{ customer.name || 'Unknown Customer' }}</div>
                    <div class="text-sm tablet:text-base desktop:text-base text-gray-500">{{ customer.email || 'No email' }}</div>
                    <!-- Show company info on mobile when company column is hidden -->
                    <div class="tablet:hidden text-xs text-gray-400 mt-1">{{ customer.name || 'Unknown Customer' }}</div>
                  </div>
                </div>
              </td>
              <td class="hidden tablet:table-cell px-6 tablet:px-6 desktop:px-8 py-4 tablet:py-4 desktop:py-5 whitespace-nowrap">
                <div class="text-sm tablet:text-base desktop:text-base text-gray-900">{{ customer.name || 'Unknown Customer' }}</div>
              </td>
              <td class="px-6 tablet:px-6 desktop:px-8 py-4 tablet:py-4 desktop:py-5 whitespace-nowrap">
                <div class="text-sm tablet:text-base desktop:text-base text-gray-900">{{ customer.contactNo || 'No contact' }}</div>
                <div class="text-sm tablet:text-base desktop:text-base text-gray-500">{{ customer.address1 || 'No address' }}</div>
                <!-- Show created date on mobile when created column is hidden -->
                <div class="desktop:hidden text-xs text-gray-400 mt-1">{{ customer.createdAt ? formatDate(new Date(customer.createdAt)) : 'No date' }}</div>
              </td>
              <td class="hidden desktop:table-cell px-6 tablet:px-6 desktop:px-8 py-4 tablet:py-4 desktop:py-5 whitespace-nowrap text-sm tablet:text-base desktop:text-base text-gray-500">
                {{ customer.createdAt ? formatDate(new Date(customer.createdAt)) : 'No date' }}
              </td>
              <td class="px-6 tablet:px-6 desktop:px-8 py-4 tablet:py-4 desktop:py-5 whitespace-nowrap text-right text-sm tablet:text-base desktop:text-base font-medium">
                <div class="flex items-center justify-end space-x-1 tablet:space-x-2 desktop:space-x-2">
                  <button
                    @click="viewCustomer(customer)"
                    class="text-blue-600 hover:text-blue-900 transition-colors p-1 tablet:p-2 desktop:p-2 rounded hover:bg-blue-50"
                    title="View Customer"
                  >
                    <svg class="w-4 h-4 tablet:w-4 tablet:h-4 desktop:w-5 desktop:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button
                    @click="editCustomer(customer)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors p-1 tablet:p-2 desktop:p-2 rounded hover:bg-indigo-50"
                    title="Edit Customer"
                  >
                    <svg class="w-4 h-4 tablet:w-4 tablet:h-4 desktop:w-5 desktop:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteCustomer(customer)"
                    class="text-red-600 hover:text-red-900 transition-colors p-1 tablet:p-2 desktop:p-2 rounded hover:bg-red-50"
                    title="Delete Customer"
                  >
                    <svg class="w-4 h-4 tablet:w-4 tablet:h-4 desktop:w-5 desktop:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Add Customer Modal -->
    <AddCustomerModal v-if="showAddModal" @close="showAddModal = false" />

    <!-- Edit Customer Modal -->
    <EditCustomerModal
      v-if="showEditModal && selectedCustomer"
      :customer="selectedCustomer"
      @close="showEditModal = false"
    />

    <!-- View Customer Modal -->
    <ViewCustomerModal
      v-if="showViewModal && selectedCustomer"
      :customer="selectedCustomer"
      @close="showViewModal = false"
      @edit="editCustomer"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { CustomerService } from '@/services/api'
import type { Customer } from '@/types'
import AddCustomerModal from '@/components/modals/AddCustomerModal.vue'
import EditCustomerModal from '@/components/modals/EditCustomerModal.vue'
import ViewCustomerModal from '@/components/modals/ViewCustomerModal.vue'

// Reactive data
const customers = ref<Customer[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const searchQuery = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const showViewModal = ref(false)
const selectedCustomer = ref<Customer | null>(null)

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

const clearSearch = () => {
  searchQuery.value = ''
}

const viewCustomer = (customer: Customer) => {
  selectedCustomer.value = customer
  showViewModal.value = true
}

const editCustomer = (customer: Customer) => {
  selectedCustomer.value = customer
  showEditModal.value = true
  showViewModal.value = false
}

const deleteCustomer = async (customer: Customer) => {
  if (confirm(`Are you sure you want to delete ${customer.name || 'this customer'}?`)) {
    try {
      const response = await CustomerService.deleteCustomer(customer.id)
      if (response.success && response.data && response.data.success) {
        // Remove from local array
        customers.value = customers.value.filter(c => c.id !== customer.id)
      } else {
        alert('Failed to delete customer: ' + (response.error || 'Unknown error'))
      }
    } catch (err) {
      alert('Failed to delete customer: ' + (err instanceof Error ? err.message : 'Unknown error'))
    }
  }
}

const loadCustomers = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await CustomerService.getCustomers()

    if (response.success && response.data && response.data.success && response.data.data) {
      customers.value = response.data.data
    } else if (response.success && response.data && response.data.success && response.data.data === null) {
      // Handle case where API returns null data (empty list)
      customers.value = []
    } else {
      error.value = response.error || 'Failed to load customers'
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load customers'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadCustomers()
})
</script>
